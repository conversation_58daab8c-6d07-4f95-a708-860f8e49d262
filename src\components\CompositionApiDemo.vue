<!-- Vue3 Composition API 演示组件 -->
<template>
  <div class="composition-api-demo p-6 bg-white rounded-lg shadow-lg">
    <h2 class="text-2xl font-bold mb-6 text-gray-800">Vue3 Composition API 演示</h2>
    
    <!-- 响应式数据演示 -->
    <div class="mb-8">
      <h3 class="text-lg font-semibold mb-4 text-blue-600">1. 响应式数据 (ref & reactive)</h3>
      <div class="bg-gray-50 p-4 rounded">
        <p class="mb-2">计数器 (ref): {{ count }}</p>
        <p class="mb-2">用户信息 (reactive): {{ user.name }} - {{ user.age }}岁</p>
        <div class="space-x-2">
          <button @click="increment" class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
            增加计数
          </button>
          <button @click="updateUser" class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600">
            更新用户
          </button>
        </div>
      </div>
    </div>

    <!-- 计算属性演示 -->
    <div class="mb-8">
      <h3 class="text-lg font-semibold mb-4 text-blue-600">2. 计算属性 (computed)</h3>
      <div class="bg-gray-50 p-4 rounded">
        <p class="mb-2">双倍计数: {{ doubleCount }}</p>
        <p class="mb-2">用户描述: {{ userDescription }}</p>
      </div>
    </div>

    <!-- 监听器演示 -->
    <div class="mb-8">
      <h3 class="text-lg font-semibold mb-4 text-blue-600">3. 监听器 (watch & watchEffect)</h3>
      <div class="bg-gray-50 p-4 rounded">
        <p class="mb-2">监听日志:</p>
        <ul class="list-disc list-inside text-sm text-gray-600">
          <li v-for="log in watchLogs" :key="log.id" class="mb-1">
            {{ log.message }} - {{ log.timestamp }}
          </li>
        </ul>
      </div>
    </div>

    <!-- 生命周期演示 -->
    <div class="mb-8">
      <h3 class="text-lg font-semibold mb-4 text-blue-600">4. 生命周期钩子</h3>
      <div class="bg-gray-50 p-4 rounded">
        <p class="mb-2">生命周期日志:</p>
        <ul class="list-disc list-inside text-sm text-gray-600">
          <li v-for="log in lifecycleLogs" :key="log.id" class="mb-1">
            {{ log.message }} - {{ log.timestamp }}
          </li>
        </ul>
      </div>
    </div>

    <!-- Vue2 vs Vue3 对比说明 -->
    <div class="mt-8 p-4 bg-yellow-50 border-l-4 border-yellow-400">
      <h4 class="font-semibold text-yellow-800 mb-2">💡 Vue2 vs Vue3 对比:</h4>
      <ul class="text-sm text-yellow-700 space-y-1">
        <li>• Vue2: 使用 Options API (data, methods, computed, watch)</li>
        <li>• Vue3: 使用 Composition API (setup函数, ref, reactive, computed, watch)</li>
        <li>• Vue3: 更好的TypeScript支持和逻辑复用</li>
        <li>• Vue3: 更好的性能和更小的包体积</li>
      </ul>
    </div>
  </div>
</template>

<script>
// Vue3 Composition API 导入
import { ref, reactive, computed, watch, watchEffect, onMounted, onUpdated, onUnmounted } from 'vue'

export default {
  name: 'CompositionApiDemo',
  setup() {
    // 1. 响应式数据
    // ref: 用于基本数据类型的响应式包装
    const count = ref(0)
    
    // reactive: 用于对象的响应式包装
    const user = reactive({
      name: '张三',
      age: 25
    })

    // 监听日志
    const watchLogs = ref([])
    const lifecycleLogs = ref([])

    // 2. 计算属性
    // computed: 基于响应式数据的计算属性
    const doubleCount = computed(() => count.value * 2)
    const userDescription = computed(() => `${user.name}今年${user.age}岁`)

    // 3. 方法
    const increment = () => {
      count.value++
    }

    const updateUser = () => {
      user.age++
      user.name = user.age % 2 === 0 ? '李四' : '张三'
    }

    const addWatchLog = (message) => {
      watchLogs.value.push({
        id: Date.now(),
        message,
        timestamp: new Date().toLocaleTimeString()
      })
    }

    const addLifecycleLog = (message) => {
      lifecycleLogs.value.push({
        id: Date.now(),
        message,
        timestamp: new Date().toLocaleTimeString()
      })
    }

    // 4. 监听器
    // watch: 监听特定的响应式数据
    watch(count, (newValue, oldValue) => {
      addWatchLog(`计数从 ${oldValue} 变为 ${newValue}`)
    })

    // 监听对象的特定属性
    watch(() => user.age, (newAge, oldAge) => {
      addWatchLog(`用户年龄从 ${oldAge} 变为 ${newAge}`)
    })

    // watchEffect: 自动追踪依赖并执行
    watchEffect(() => {
      if (count.value > 0 && user.age > 25) {
        addWatchLog(`计数器(${count.value})和用户年龄(${user.age})都满足条件`)
      }
    })

    // 5. 生命周期钩子
    onMounted(() => {
      addLifecycleLog('组件已挂载 (onMounted)')
    })

    onUpdated(() => {
      addLifecycleLog('组件已更新 (onUpdated)')
    })

    onUnmounted(() => {
      console.log('组件即将卸载 (onUnmounted)')
    })

    // 返回模板中需要使用的数据和方法
    return {
      // 响应式数据
      count,
      user,
      watchLogs,
      lifecycleLogs,
      
      // 计算属性
      doubleCount,
      userDescription,
      
      // 方法
      increment,
      updateUser
    }
  }
}
</script>

<style scoped>
/* 组件样式 */
.composition-api-demo {
  max-width: 800px;
  margin: 0 auto;
}

/* 按钮悬停效果 */
button:hover {
  transform: translateY(-1px);
  transition: all 0.2s ease;
}

/* 日志列表样式 */
ul {
  max-height: 200px;
  overflow-y: auto;
}
</style>
