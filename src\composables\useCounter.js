// src/composables/useCounter.js
import { ref, computed } from 'vue';

// 按照惯例，组合式函数名以“use”开头
export function useCounter(initialValue = 0, step = 1) {
  // 封装在函数中的 state
  const count = ref(initialValue);

  // 组合式函数可以返回计算属性
  const doubleCount = computed(() => count.value * 2);

  // 组合式函数可以暴露方法来修改 state
  function increment() {
    count.value += step;
  }

  function decrement() {
    count.value -= step;
  }

  // 通过返回一个对象来暴露 state 和方法
  return {
    count,
    doubleCount,
    increment,
    decrement
  };
}
