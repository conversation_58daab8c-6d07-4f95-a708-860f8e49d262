/**
 * 本地存储 Composable
 * 提供响应式的本地存储功能
 */
import { ref, watch } from 'vue'

export function useLocalStorage(key, defaultValue = null) {
  // 从本地存储读取初始值
  const storedValue = localStorage.getItem(key)
  const initialValue = storedValue ? JSON.parse(storedValue) : defaultValue
  
  // 创建响应式引用
  const value = ref(initialValue)
  
  // 监听值的变化，自动保存到本地存储
  watch(value, (newValue) => {
    if (newValue === null || newValue === undefined) {
      localStorage.removeItem(key)
    } else {
      localStorage.setItem(key, JSON.stringify(newValue))
    }
  }, { deep: true })
  
  // 手动设置值的方法
  const setValue = (newValue) => {
    value.value = newValue
  }
  
  // 删除存储的方法
  const removeValue = () => {
    value.value = null
  }
  
  return {
    value,
    setValue,
    removeValue
  }
}
