/**
 * API 请求 Composable
 * 提供统一的API请求处理，包含加载状态、错误处理等
 */
import { ref, reactive } from 'vue'

export function useApi() {
  // 响应式状态
  const state = reactive({
    loading: false,
    error: null,
    data: null
  })
  
  // 执行API请求的方法
  const execute = async (apiCall) => {
    state.loading = true
    state.error = null
    
    try {
      const result = await apiCall()
      state.data = result
      return result
    } catch (error) {
      state.error = error.message || '请求失败'
      throw error
    } finally {
      state.loading = false
    }
  }
  
  // 重置状态
  const reset = () => {
    state.loading = false
    state.error = null
    state.data = null
  }
  
  return {
    ...state,
    execute,
    reset
  }
}

/**
 * 模拟API调用的工具函数
 */
export function mockApiCall(data, delay = 1000, shouldFail = false) {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      if (shouldFail) {
        reject(new Error('模拟API调用失败'))
      } else {
        resolve(data)
      }
    }, delay)
  })
}
