# Vue 2 vs Vue 3 主要特性对比示例

## 概述

本项目展示了 Vue 2 和 Vue 3 之间的主要差异，通过实际代码示例帮助开发者理解两个版本的区别和升级要点。

## 示例组件说明

### 1. Vue3Examples.vue - Vue 3 特性展示
展示Vue 3的核心新特性：

#### 🚀 Composition API
- 使用 `<script setup>` 语法
- `ref()` 和 `reactive()` 响应式系统
- `computed()` 计算属性
- 组合式函数的使用

```vue
<script setup>
import { ref, computed } from 'vue'

const count = ref(0)
const doubleCount = computed(() => count.value * 2)
</script>
```

#### 🔄 新的响应式系统
- 基于 Proxy 的响应式
- 支持动态属性添加
- 更好的类型推断

#### 🎯 生命周期钩子
- `onMounted()`, `onUpdated()`, `onUnmounted()`
- 在 setup 函数中使用

#### 🎣 组合式函数 (Composables)
- `useMouse()` 鼠标位置追踪
- 逻辑复用的新方式
- 替代 mixins

#### 🚀 Teleport 传送门
- 将组件渲染到指定 DOM 位置
- 解决模态框、提示框等 z-index 问题

```vue
<Teleport to="body">
  <div class="modal">Modal 内容</div>
</Teleport>
```

#### 🧩 Fragment 多根节点
- 支持多个根节点
- 无需包装 div

### 2. Vue2Examples.vue - Vue 2 风格展示
展示Vue 2的传统写法：

#### 📦 Options API
- `data()`, `computed`, `methods`
- 传统的选项式写法

```javascript
export default {
  data() {
    return {
      count: 0
    }
  },
  computed: {
    doubleCount() {
      return this.count * 2
    }
  }
}
```

#### 🔀 Mixins 代码复用
- 通过 mixins 共享逻辑
- 存在命名冲突风险

#### 🔒 单一根节点限制
- 组件必须有唯一根元素

### 3. VueComparison.vue - 详细对比说明
提供全面的对比分析：

#### 📊 对比表格
- API 风格差异
- 响应式系统对比
- 性能和包体积比较

#### 💡 迁移建议
- 何时升级到 Vue 3
- 何时继续使用 Vue 2

## 核心组件文件

### useMouse.js - 组合式函数示例
```javascript
import { ref, onMounted, onUnmounted } from 'vue'

export function useMouse() {
  const x = ref(0)
  const y = ref(0)

  function update(event) {
    x.value = event.pageX
    y.value = event.pageY
  }

  onMounted(() => window.addEventListener('mousemove', update))
  onUnmounted(() => window.removeEventListener('mousemove', update))

  return { x, y }
}
```

### FragmentExample.vue - 多根节点示例
```vue
<template>
  <!-- Vue 3 支持多根节点 -->
  <p>第一个根节点</p>
  <p>第二个根节点</p>
  <p>第三个根节点</p>
</template>
```

## 主要差异总结

### 🎯 API 设计
| 特性 | Vue 2 | Vue 3 |
|------|-------|-------|
| API 风格 | Options API | Composition API + Options API |
| 代码复用 | Mixins | Composables |
| TypeScript | 社区支持 | 原生支持 |

### ⚡ 性能优化
- **包体积**: Vue 3 减少 60% (Tree-shaking)
- **运行时性能**: 提升 20-40%
- **编译优化**: 静态提升、内联组件 props

### 🆕 新特性
- **Fragment**: 多根节点支持
- **Teleport**: 组件传送门
- **Suspense**: 异步组件处理
- **更好的 Tree-shaking**: 按需打包

### 🔧 开发体验
- **更好的 TypeScript 支持**
- **改进的开发工具**
- **更清晰的响应式系统**

## 使用建议

### ✅ 推荐升级 Vue 3 的场景
- 新项目开发
- 需要更好的 TypeScript 支持
- 追求更高性能
- 需要使用新特性

### ⚠️ 继续使用 Vue 2 的场景
- 大型现有项目
- 依赖大量 Vue 2 生态
- 团队学习成本考虑
- 需要 IE11 支持

## 运行项目

```bash
npm install
npm run dev
```

访问以下路由查看示例：
- `/vue3-examples` - Vue 3 特性示例
- `/vue2-examples` - Vue 2 风格示例  
- `/vue-comparison` - 详细对比说明

## 技术栈

- Vue 3.x
- Vue Router 4.x
- Tailwind CSS
- Vite

---

通过这些示例，您可以直观地了解 Vue 2 和 Vue 3 的区别，并根据项目需求做出合适的选择。 