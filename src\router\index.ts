import { createRouter, createWebHistory } from 'vue-router'

const routes = [
    {
        path: '/',
        name: 'home',
        component: () => import('../components/Home.vue'),
        meta: { title: '首页' }
    },
    {
        path: '/composition-api',
        name: 'composition-api',
        component: () => import('../components/CompositionApiDemo.vue'),
        meta: { title: 'Composition API 演示' }
    },
    {
        path: '/vue3-features',
        name: 'vue3-features',
        component: () => import('../components/Vue3FeaturesDemo.vue'),
        meta: { title: 'Vue3 新特性演示' }
    },
    {
        path: '/composables',
        name: 'composables',
        component: () => import('../components/ComposablesDemo.vue'),
        meta: { title: 'Composables 演示' }
    },
    {
        path: '/about',
        name: 'about',
        component: () => import('../components/About.vue'),
        meta: { title: '关于' }
    },
    {
        path: '/horoscope',
        name: 'horoscope',
        component: () => import('../components/Horoscope.vue'),
        meta: { title: '星座运势' }
    },
    {
        path: '/watch',
        name: 'watch',
        component: () => import('../components/Watch.vue'),
        meta: { title: '查看' }
    },
    {
        path: '/com',
        name: 'com',
        component: () => import('../components/Com.vue'),
        meta: { title: '查看' }
    }
];

const router = createRouter({
    history: createWebHistory(),
    routes
});

// 全局前置守卫，用于更新页面标题
router.beforeEach((to, from, next) => {
    if (to.meta.title) {
        document.title = `${to.meta.title} - Vue 3 示例`;
    }
    next();
});

export default router