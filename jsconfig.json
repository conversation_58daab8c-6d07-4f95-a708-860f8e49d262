{"compilerOptions": {"target": "ES2020", "module": "ES2022", "lib": ["ES2020", "DOM", "DOM.Iterable"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": false, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "preserve", "paths": {"@/*": ["./src/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}