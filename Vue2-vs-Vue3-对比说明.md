# Vue2 vs Vue3 详细对比说明

## 📋 目录
1. [核心架构变化](#核心架构变化)
2. [API对比](#api对比)
3. [性能提升](#性能提升)
4. [新特性](#新特性)
5. [迁移指南](#迁移指南)
6. [最佳实践](#最佳实践)

## 🏗️ 核心架构变化

### 1. 响应式系统
**Vue2 (Object.defineProperty)**
```javascript
// Vue2 响应式原理
Object.defineProperty(obj, 'count', {
  get() {
    return value
  },
  set(newValue) {
    value = newValue
    // 触发更新
  }
})
```

**Vue3 (Proxy)**
```javascript
// Vue3 响应式原理
const reactive = new Proxy(target, {
  get(target, key) {
    track(target, key) // 依赖收集
    return target[key]
  },
  set(target, key, value) {
    target[key] = value
    trigger(target, key) // 触发更新
    return true
  }
})
```

### 2. 组件定义方式

**Vue2 Options API**
```javascript
export default {
  data() {
    return {
      count: 0,
      user: { name: '张三' }
    }
  },
  computed: {
    doubleCount() {
      return this.count * 2
    }
  },
  methods: {
    increment() {
      this.count++
    }
  },
  watch: {
    count(newVal, oldVal) {
      console.log(`计数从 ${oldVal} 变为 ${newVal}`)
    }
  },
  mounted() {
    console.log('组件已挂载')
  }
}
```

**Vue3 Composition API**
```javascript
import { ref, reactive, computed, watch, onMounted } from 'vue'

export default {
  setup() {
    // 响应式数据
    const count = ref(0)
    const user = reactive({ name: '张三' })
    
    // 计算属性
    const doubleCount = computed(() => count.value * 2)
    
    // 方法
    const increment = () => {
      count.value++
    }
    
    // 监听器
    watch(count, (newVal, oldVal) => {
      console.log(`计数从 ${oldVal} 变为 ${newVal}`)
    })
    
    // 生命周期
    onMounted(() => {
      console.log('组件已挂载')
    })
    
    return {
      count,
      user,
      doubleCount,
      increment
    }
  }
}
```

## 🔄 API对比

### 生命周期钩子对比

| Vue2 | Vue3 | 说明 |
|------|------|------|
| beforeCreate | setup() | 组件创建前 |
| created | setup() | 组件创建后 |
| beforeMount | onBeforeMount | 挂载前 |
| mounted | onMounted | 挂载后 |
| beforeUpdate | onBeforeUpdate | 更新前 |
| updated | onUpdated | 更新后 |
| beforeDestroy | onBeforeUnmount | 卸载前 |
| destroyed | onUnmounted | 卸载后 |

### 响应式API对比

| 功能 | Vue2 | Vue3 |
|------|------|------|
| 响应式数据 | data() | ref(), reactive() |
| 计算属性 | computed | computed() |
| 监听器 | watch | watch(), watchEffect() |
| 方法 | methods | 普通函数 |

## 🚀 性能提升

### 1. 包体积优化
- **Vue2**: 完整版本 ~34KB (gzipped)
- **Vue3**: 完整版本 ~34KB，但支持更好的 Tree-shaking
- **Vue3**: 最小运行时 ~10KB (gzipped)

### 2. 渲染性能
- **编译时优化**: 静态提升、补丁标记、块级更新
- **更快的组件初始化**: Composition API 减少了组件实例的创建开销
- **更好的内存使用**: Proxy 相比 Object.defineProperty 更高效

### 3. TypeScript 支持
- **Vue2**: 需要额外配置，类型推导有限
- **Vue3**: 原生 TypeScript 支持，更好的类型推导

## ✨ 新特性

### 1. Fragment (多根节点)
**Vue2**
```vue
<template>
  <div> <!-- 必须有根元素 -->
    <p>第一段</p>
    <p>第二段</p>
  </div>
</template>
```

**Vue3**
```vue
<template>
  <p>第一段</p>
  <p>第二段</p>
  <!-- 不需要根元素 -->
</template>
```

### 2. Teleport (传送门)
```vue
<template>
  <div>
    <h1>组件内容</h1>
    <!-- 将模态框传送到 body -->
    <Teleport to="body">
      <div class="modal">模态框内容</div>
    </Teleport>
  </div>
</template>
```

### 3. Suspense (异步组件)
```vue
<template>
  <Suspense>
    <template #default>
      <AsyncComponent />
    </template>
    <template #fallback>
      <div>加载中...</div>
    </template>
  </Suspense>
</template>
```

### 4. 自定义指令变化
**Vue2**
```javascript
Vue.directive('focus', {
  inserted(el) {
    el.focus()
  }
})
```

**Vue3**
```javascript
app.directive('focus', {
  mounted(el) { // inserted -> mounted
    el.focus()
  }
})
```

## 🔄 迁移指南

### 1. 破坏性变化
- **全局API**: `Vue.xxx` → `app.xxx`
- **事件API**: `$on`, `$off`, `$once` 被移除
- **过滤器**: 被移除，使用计算属性或方法替代
- **内联模板**: 被移除

### 2. 迁移步骤
1. **升级构建工具**: 使用 Vite 或升级 webpack
2. **更新依赖**: 升级 Vue Router、Vuex 等
3. **逐步迁移**: 可以在 Vue3 中使用 Options API
4. **重构为 Composition API**: 逐步重构复杂组件

### 3. 兼容性
- **IE11**: Vue3 不支持 IE11
- **现代浏览器**: 完全支持
- **Node.js**: 需要 Node.js 12+

## 💡 最佳实践

### 1. 何时使用 Composition API
- ✅ 复杂的组件逻辑
- ✅ 逻辑复用需求
- ✅ TypeScript 项目
- ✅ 大型应用

### 2. 何时使用 Options API
- ✅ 简单组件
- ✅ 团队熟悉度高
- ✅ 快速原型开发

### 3. 组合使用
```javascript
export default {
  // 可以同时使用两种API
  props: ['title'],
  setup(props) {
    // Composition API 逻辑
    const count = ref(0)
    return { count }
  },
  computed: {
    // Options API 计算属性
    displayTitle() {
      return this.title.toUpperCase()
    }
  }
}
```

## 📊 总结对比表

| 特性 | Vue2 | Vue3 | 优势 |
|------|------|------|------|
| 学习曲线 | 较平缓 | 稍陡峭 | Vue2 更容易上手 |
| 代码组织 | Options API | Composition API | Vue3 更灵活 |
| 性能 | 良好 | 更好 | Vue3 性能提升明显 |
| 包体积 | 较大 | 更小 | Vue3 支持更好的 Tree-shaking |
| TypeScript | 有限支持 | 原生支持 | Vue3 类型安全更好 |
| 生态系统 | 成熟 | 快速发展 | Vue2 生态更完善 |
| 维护状态 | LTS 支持到 2023 年底 | 活跃开发 | Vue3 是未来方向 |

## 🎯 选择建议

### 选择 Vue2 的场景
- 现有大型项目，迁移成本高
- 团队对 Vue2 非常熟悉
- 需要 IE11 支持
- 依赖的第三方库还未支持 Vue3

### 选择 Vue3 的场景
- 新项目开发
- 需要更好的性能
- 使用 TypeScript
- 需要更好的逻辑复用
- 追求最新技术栈

Vue3 是 Vue.js 的未来，建议新项目优先选择 Vue3，现有项目可以根据实际情况逐步迁移。
