<template>
  <div class="text-center p-8">
    <h1 class="text-4xl font-bold mb-6 bg-gradient-to-r from-blue-600 to-green-600 bg-clip-text text-transparent">
      欢迎来到 Vue 3 示例项目
    </h1>
    <p class="text-lg text-gray-600 mb-6">
      这个项目旨在展示 Vue 3 的各种实用技巧和最佳实践，以及与 Vue 2 的详细对比。
    </p>

    <!-- 示例卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8 max-w-6xl mx-auto">
      <!-- Vue 3 特性示例 -->
      <div class="bg-white rounded-lg shadow-md p-6 border-t-4 border-blue-500">
        <div class="text-4xl mb-4">🚀</div>
        <h3 class="text-xl font-semibold text-blue-600 mb-3">Vue 3 特性示例</h3>
        <p class="text-gray-600 mb-4">
          体验 Composition API、响应式系统、Teleport、Fragment 等 Vue 3 核心特性
        </p>
        <router-link 
          to="/vue3-examples" 
          class="inline-block bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition-colors"
        >
          查看示例
        </router-link>
      </div>

      <!-- Vue 2 风格示例 -->
      <div class="bg-white rounded-lg shadow-md p-6 border-t-4 border-orange-500">
        <div class="text-4xl mb-4">📦</div>
        <h3 class="text-xl font-semibold text-orange-600 mb-3">Vue 2 风格示例</h3>
        <p class="text-gray-600 mb-4">
          回顾 Options API、Mixins 等 Vue 2 传统写法，了解两个版本的差异
        </p>
        <router-link 
          to="/vue2-examples" 
          class="inline-block bg-orange-500 text-white px-4 py-2 rounded hover:bg-orange-600 transition-colors"
        >
          查看示例
        </router-link>
      </div>

      <!-- 详细对比 -->
      <div class="bg-white rounded-lg shadow-md p-6 border-t-4 border-green-500">
        <div class="text-4xl mb-4">⚖️</div>
        <h3 class="text-xl font-semibold text-green-600 mb-3">Vue 2 vs Vue 3 对比</h3>
        <p class="text-gray-600 mb-4">
          深入了解两个版本的区别、性能对比和迁移建议
        </p>
        <router-link 
          to="/vue-comparison" 
          class="inline-block bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 transition-colors"
        >
          查看对比
        </router-link>
      </div>
    </div>

    <!-- 快速导航 -->
    <div class="mt-12 bg-gray-50 rounded-lg p-6">
      <h2 class="text-2xl font-semibold text-gray-800 mb-4">快速导航</h2>
      <div class="flex flex-wrap justify-center gap-4">
        <router-link to="/suspense" class="bg-blue-100 text-blue-800 px-4 py-2 rounded-full hover:bg-blue-200 transition-colors">
          ⏸️ 异步组件
        </router-link>
        <router-link to="/horoscope" class="bg-purple-100 text-purple-800 px-4 py-2 rounded-full hover:bg-purple-200 transition-colors">
          ⭐ 星座运势
        </router-link>
        <router-link to="/about" class="bg-gray-100 text-gray-800 px-4 py-2 rounded-full hover:bg-gray-200 transition-colors">
          ℹ️ 关于项目
        </router-link>
      </div>
    </div>

    <!-- 特性概览 -->
    <div class="mt-8 text-left max-w-4xl mx-auto">
      <h2 class="text-2xl font-semibold text-gray-800 mb-4 text-center">本项目展示的主要特性</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <h3 class="text-lg font-medium text-blue-600 mb-2">Vue 3 新特性</h3>
          <ul class="text-gray-600 space-y-1">
            <li>✨ Composition API 组合式 API</li>
            <li>🔄 Proxy 响应式系统</li>
            <li>🚀 Teleport 传送门</li>
            <li>🧩 Fragment 多根节点</li>
            <li>⏸️ Suspense 异步处理</li>
            <li>🎣 Composables 组合式函数</li>
          </ul>
        </div>
        <div>
          <h3 class="text-lg font-medium text-orange-600 mb-2">Vue 2 对比</h3>
          <ul class="text-gray-600 space-y-1">
            <li>📦 Options API 选项式 API</li>
            <li>🔀 Mixins 混入</li>
            <li>🔒 单根节点限制</li>
            <li>🎯 生命周期钩子差异</li>
            <li>📈 性能和包体积对比</li>
            <li>🚀 迁移策略建议</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// 欢迎页面，展示项目概览和导航
</script>