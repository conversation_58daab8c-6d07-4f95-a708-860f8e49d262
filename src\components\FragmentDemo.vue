<!-- Vue3 Fragment 演示组件 -->
<template>
  <!-- Vue3 支持多个根节点，这就是 Fragment 特性 -->
  <p class="text-green-600 mb-2">✅ 第一个根节点</p>
  <p class="text-blue-600 mb-2">✅ 第二个根节点</p>
  <p class="text-red-600 mb-2">✅ 第三个根节点</p>
  <div class="text-sm text-gray-500 mt-2 p-2 bg-yellow-50 rounded">
    💡 在Vue2中，这需要一个包装div，但Vue3支持多根节点！
  </div>
</template>

<script setup>
// Vue3 Fragment 特性演示
// 这个组件有多个根节点，在Vue2中是不被允许的
</script>

<style scoped>
/* Fragment 组件样式 */
</style>
