<template>
  <div id="app">
    <nav class="bg-gray-800 text-white p-4">
      <div class="container mx-auto flex justify-between items-center">
        <router-link to="/" class="text-xl font-bold">Vue 3 示例</router-link>
        <div class="hidden md:flex space-x-4">
          <router-link to="/" class="hover:text-gray-300">首页</router-link>
          <div class="relative group">
            <span class="hover:text-gray-300 cursor-pointer">Vue3 演示</span>
            <div class="absolute hidden group-hover:block bg-gray-800 text-white mt-2 rounded shadow-lg w-56 z-10">
              <router-link v-for="example in vue3Examples" :key="example.path" :to="example.path" class="block px-4 py-2 hover:bg-gray-700">
                {{ example.name }}
              </router-link>
            </div>
          </div>
          <router-link to="/about" class="hover:text-gray-300">关于</router-link>
          <router-link to="/horoscope" class="hover:text-gray-300">星座运势</router-link>
        </div>
        <button @click="toggleMenu" class="md:hidden">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16m-7 6h7" />
          </svg>
        </button>
      </div>
      <div v-if="isMenuOpen" class="md:hidden mt-2">
        <router-link to="/" class="block py-2 px-4 hover:bg-gray-700">首页</router-link>
        <h3 class="px-4 py-2 text-gray-400 text-sm">Vue3 演示</h3>
        <router-link v-for="example in vue3Examples" :key="example.path" :to="example.path" class="block py-2 px-4 hover:bg-gray-700">
          {{ example.name }}
        </router-link>
        <router-link to="/about" class="block py-2 px-4 hover:bg-gray-700">关于</router-link>
        <router-link to="/horoscope" class="block py-2 px-4 hover:bg-gray-700">星座运势</router-link>
      </div>
    </nav>

    <main class="container mx-auto p-4">
      <router-view />
    </main>
  </div>
</template>

<script setup>
import { ref } from 'vue';

const isMenuOpen = ref(false);

const vue3Examples = [
  { path: '/composition-api', name: 'Composition API 演示' },
  { path: '/vue3-features', name: 'Vue3 新特性演示' },
  { path: '/composables', name: 'Composables 演示' }
];

function toggleMenu() {
  isMenuOpen.value = !isMenuOpen.value;
}
</script>

<style>
.router-link-active {
  color: #42b983; /* Vue Green */
}
</style>
