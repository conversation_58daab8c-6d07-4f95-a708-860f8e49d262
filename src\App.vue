<template>
  <div id="app">
    <nav class="bg-gray-800 text-white p-4">
      <div class="container mx-auto flex justify-between items-center">
        <router-link to="/" class="text-xl font-bold">Vue 3 示例</router-link>
        <div class="hidden md:flex space-x-4">
          <router-link to="/" class="hover:text-gray-300 px-3 py-2 rounded transition-colors">首页</router-link>
          <router-link to="/composition-api" class="hover:text-gray-300 px-3 py-2 rounded transition-colors">Composition API</router-link>
          <router-link to="/vue3-features" class="hover:text-gray-300 px-3 py-2 rounded transition-colors">Vue3 新特性</router-link>
          <router-link to="/composables" class="hover:text-gray-300 px-3 py-2 rounded transition-colors">Composables</router-link>
          <router-link to="/about" class="hover:text-gray-300 px-3 py-2 rounded transition-colors">关于</router-link>
        </div>
        <button @click="toggleMenu" class="md:hidden">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16m-7 6h7" />
          </svg>
        </button>
      </div>
      <div v-if="isMenuOpen" class="md:hidden mt-2 space-y-1">
        <router-link to="/" class="block py-2 px-4 hover:bg-gray-700 rounded transition-colors">首页</router-link>
        <router-link to="/composition-api" class="block py-2 px-4 hover:bg-gray-700 rounded transition-colors">Composition API</router-link>
        <router-link to="/vue3-features" class="block py-2 px-4 hover:bg-gray-700 rounded transition-colors">Vue3 新特性</router-link>
        <router-link to="/composables" class="block py-2 px-4 hover:bg-gray-700 rounded transition-colors">Composables</router-link>
        <router-link to="/about" class="block py-2 px-4 hover:bg-gray-700 rounded transition-colors">关于</router-link>
      </div>
    </nav>

    <main class="container mx-auto p-4">
      <router-view />
    </main>
  </div>
</template>

<script setup>
import { ref } from 'vue';

const isMenuOpen = ref(false);

// 移动端菜单状态

function toggleMenu() {
  isMenuOpen.value = !isMenuOpen.value;
}
</script>

<style>
.router-link-active {
  color: #42b983 !important; /* Vue Green */
  background-color: rgba(66, 185, 131, 0.1);
}

/* 导航链接悬停效果 */
nav a:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* 移动端菜单动画 */
.md\:hidden {
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
