<!-- Composables 演示组件 -->
<template>
  <div class="composables-demo p-6 bg-white rounded-lg shadow-lg">
    <h2 class="text-2xl font-bold mb-6 text-gray-800">自定义 Composables 演示</h2>

    <!-- useLocalStorage 演示 -->
    <div class="mb-8">
      <h3 class="text-lg font-semibold mb-4 text-green-600">1. useLocalStorage - 本地存储</h3>
      <div class="bg-gray-50 p-4 rounded">
        <div class="mb-4">
          <label class="block text-sm font-medium mb-2">用户名:</label>
          <input 
            v-model="username.value"
            placeholder="输入用户名，会自动保存到本地存储"
            class="w-full p-2 border border-gray-300 rounded"
          />
        </div>
        <div class="mb-4">
          <label class="block text-sm font-medium mb-2">用户设置:</label>
          <div class="space-y-2">
            <label class="flex items-center">
              <input 
                type="checkbox" 
                v-model="userSettings.value.darkMode"
                class="mr-2"
              />
              深色模式
            </label>
            <label class="flex items-center">
              <input 
                type="checkbox" 
                v-model="userSettings.value.notifications"
                class="mr-2"
              />
              通知提醒
            </label>
          </div>
        </div>
        <div class="space-x-2">
          <button 
            @click="clearUsername"
            class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
          >
            清除用户名
          </button>
          <button 
            @click="clearSettings"
            class="px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600"
          >
            重置设置
          </button>
        </div>
      </div>
    </div>

    <!-- useToggle 演示 -->
    <div class="mb-8">
      <h3 class="text-lg font-semibold mb-4 text-green-600">2. useToggle - 状态切换</h3>
      <div class="bg-gray-50 p-4 rounded">
        <div class="mb-4">
          <p class="mb-2">显示状态: {{ isVisible.value ? '显示' : '隐藏' }}</p>
          <div v-if="isVisible.value" class="p-3 bg-blue-100 rounded mb-2">
            这是一个可切换显示的内容块
          </div>
        </div>
        <div class="space-x-2">
          <button 
            @click="toggleVisibility"
            class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            切换显示
          </button>
          <button 
            @click="showContent"
            class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
          >
            显示
          </button>
          <button 
            @click="hideContent"
            class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
          >
            隐藏
          </button>
        </div>
      </div>
    </div>

    <!-- useApi 演示 -->
    <div class="mb-8">
      <h3 class="text-lg font-semibold mb-4 text-green-600">3. useApi - API请求管理</h3>
      <div class="bg-gray-50 p-4 rounded">
        <div class="mb-4">
          <p class="mb-2">API状态:</p>
          <div class="text-sm space-y-1">
            <p>加载中: {{ loading ? '是' : '否' }}</p>
            <p>错误: {{ error || '无' }}</p>
            <p>数据: {{ data ? JSON.stringify(data) : '无' }}</p>
          </div>
        </div>
        <div class="space-x-2">
          <button 
            @click="fetchSuccess"
            :disabled="loading"
            class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50"
          >
            成功请求
          </button>
          <button 
            @click="fetchError"
            :disabled="loading"
            class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 disabled:opacity-50"
          >
            失败请求
          </button>
          <button 
            @click="resetApi"
            class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
          >
            重置状态
          </button>
        </div>
      </div>
    </div>

    <!-- useMouse 演示 -->
    <div class="mb-8">
      <h3 class="text-lg font-semibold mb-4 text-green-600">4. useMouse - 鼠标位置追踪</h3>
      <div class="bg-gray-50 p-4 rounded">
        <div class="mb-4 h-32 bg-blue-100 rounded flex items-center justify-center relative">
          <p class="text-blue-800">鼠标位置: ({{ x }}, {{ y }})</p>
          <div 
            class="absolute w-2 h-2 bg-red-500 rounded-full pointer-events-none"
            :style="{ left: x + 'px', top: y + 'px', transform: 'translate(-50%, -50%)' }"
          ></div>
        </div>
        <p class="text-sm text-gray-600">在上方区域移动鼠标查看坐标变化</p>
      </div>
    </div>

    <!-- Vue2 vs Vue3 对比说明 -->
    <div class="mt-8 p-4 bg-green-50 border-l-4 border-green-400">
      <h4 class="font-semibold text-green-800 mb-2">🔧 Composables 优势:</h4>
      <ul class="text-sm text-green-700 space-y-1">
        <li>• Vue2: 使用 mixins 进行逻辑复用，容易产生命名冲突</li>
        <li>• Vue3: 使用 Composables 进行逻辑复用，更清晰的依赖关系</li>
        <li>• 更好的类型推导和IDE支持</li>
        <li>• 可以在任何地方使用，不仅限于组件内部</li>
        <li>• 更容易测试和维护</li>
      </ul>
    </div>
  </div>
</template>

<script>
import { useLocalStorage } from '../composables/useLocalStorage.js'
import { useToggle } from '../composables/useToggle.js'
import { useApi, mockApiCall } from '../composables/useApi.js'
import { useMouse } from '../composables/useMouse.js'

export default {
  name: 'ComposablesDemo',
  setup() {
    // 1. 使用本地存储 Composable
    const username = useLocalStorage('demo-username', '')
    const userSettings = useLocalStorage('demo-settings', {
      darkMode: false,
      notifications: true
    })

    const clearUsername = () => {
      username.removeValue()
    }

    const clearSettings = () => {
      userSettings.removeValue()
    }

    // 2. 使用切换状态 Composable
    const { 
      value: isVisible, 
      toggle: toggleVisibility, 
      setTrue: showContent, 
      setFalse: hideContent 
    } = useToggle(true)

    // 3. 使用API请求 Composable
    const { loading, error, data, execute, reset: resetApi } = useApi()

    const fetchSuccess = async () => {
      try {
        await execute(() => mockApiCall({ message: '请求成功!', timestamp: new Date().toISOString() }))
      } catch (err) {
        console.error('请求失败:', err)
      }
    }

    const fetchError = async () => {
      try {
        await execute(() => mockApiCall(null, 1000, true))
      } catch (err) {
        console.error('请求失败:', err)
      }
    }

    // 4. 使用鼠标位置 Composable
    const { x, y } = useMouse()

    return {
      // 本地存储
      username,
      userSettings,
      clearUsername,
      clearSettings,
      
      // 切换状态
      isVisible,
      toggleVisibility,
      showContent,
      hideContent,
      
      // API请求
      loading,
      error,
      data,
      fetchSuccess,
      fetchError,
      resetApi,
      
      // 鼠标位置
      x,
      y
    }
  }
}
</script>

<style scoped>
.composables-demo {
  max-width: 800px;
  margin: 0 auto;
}

button:hover {
  transform: translateY(-1px);
  transition: all 0.2s ease;
}

button:disabled {
  transform: none;
  cursor: not-allowed;
}
</style>
