<template>
  <div class="max-w-3xl mx-auto p-8">
    <h1 class="text-3xl font-bold mb-4">关于此项目</h1>
    <p class="text-lg mb-4">
      本项目是一个使用 Vue 3、Vite 和 Tailwind CSS 构建的单页应用，旨在提供一系列关于 Vue 3 核心功能的中文示例和说明。
    </p>
    <h2 class="text-2xl font-bold mt-6 mb-3">包含的示例</h2>
    <ul class="list-disc list-inside space-y-2">
      <li><b>响应式系统:</b> 深入理解 <code>ref</code> 和 <code>reactive</code>。</li>
      <li><b>计算属性:</b> 如何使用 <code>computed</code> 来派生状态。</li>
      <li><b>侦听器:</b> <code>watch</code> 和 <code>watchEffect</code> 的应用场景。</li>
      <li><b>组件通信:</b> 掌握 <code>props</code>, <code>emits</code>, 和 <code>provide/inject</code>。</li>
      <li><b>生命周期:</b> 了解组件从创建到销毁的整个过程。</li>
      <li><b>组合式 API:</b> 如何通过 <code>composables</code> 复用和组织逻辑。</li>
      <li><b>Teleport:</b> 将组件渲染到 DOM 的任意位置。</li>
      <li><b>Suspense:</b> 优雅地处理异步组件的加载状态。</li>
    </ul>
    <p class="mt-6">
      希望这些示例能帮助你更好地理解和使用 Vue 3。
    </p>
  </div>
</template>

<script setup>
// 关于页面，无需特定逻辑
</script>
