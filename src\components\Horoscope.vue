<template>
  <div class="max-w-4xl mx-auto px-4 py-8">
    <!-- 页面标题 -->
    <header class="text-center mb-10">
      <h1 class="text-5xl font-bold mb-4 bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
        ⭐ 星座运势查询
      </h1>
      <p class="text-gray-600 text-lg">探索您的星座运势，了解今日、明日及本周本月的运势变化</p>
    </header>

    <!-- 选择器区域 -->
    <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- 星座选择 -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">选择星座</label>
          <select 
            v-model="selectedConstellation" 
            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors"
          >
            <option value="">请选择星座</option>
            <option v-for="constellation in constellations" :key="constellation.value" :value="constellation.value">
              {{ constellation.emoji }} {{ constellation.name }}
            </option>
          </select>
        </div>

        <!-- 时间选择 -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">选择时间</label>
          <select 
            v-model="selectedTime" 
            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors"
          >
            <option value="">请选择时间</option>
            <option v-for="time in timeOptions" :key="time.value" :value="time.value">
              {{ time.name }}
            </option>
          </select>
        </div>
      </div>

      <!-- 查询按钮 -->
      <div class="text-center mt-6">
        <button 
          @click="queryHoroscope" 
          :disabled="!selectedConstellation || !selectedTime || loading"
          class="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-3 rounded-lg font-semibold hover:from-purple-700 hover:to-pink-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all transform hover:scale-105"
        >
          <span v-if="loading">🔍 查询中...</span>
          <span v-else>🔮 查询运势</span>
        </button>
      </div>
    </div>

    <!-- 结果展示区域 -->
    <div v-if="horoscopeData" class="bg-white rounded-lg shadow-lg p-6">
      <!-- 标题和时间 -->
      <div class="text-center mb-8">
        <h2 class="text-3xl font-bold text-gray-800 mb-2">
          {{ horoscopeData.title }} {{ getTimeText(selectedTime) }}
        </h2>
        <p class="text-gray-600">{{ horoscopeData.time }}</p>
        <p class="text-lg text-purple-600 font-semibold mt-2">{{ horoscopeData.shortcomment }}</p>
      </div>

      <!-- 幸运信息 -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
        <div class="bg-gradient-to-r from-yellow-400 to-orange-500 text-white p-4 rounded-lg text-center">
          <div class="text-2xl font-bold">{{ horoscopeData.luckynumber }}</div>
          <div class="text-sm">幸运数字</div>
        </div>
        <div class="bg-gradient-to-r from-blue-400 to-purple-500 text-white p-4 rounded-lg text-center">
          <div class="text-lg font-bold">{{ horoscopeData.luckycolor }}</div>
          <div class="text-sm">幸运色彩</div>
        </div>
        <div class="bg-gradient-to-r from-green-400 to-teal-500 text-white p-4 rounded-lg text-center">
          <div class="text-lg font-bold">{{ horoscopeData.luckyconstellation }}</div>
          <div class="text-sm">幸运星座</div>
        </div>
      </div>

      <!-- 运势指数 -->
      <div class="mb-8">
        <h3 class="text-xl font-bold text-gray-800 mb-4">运势指数</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="space-y-3">
            <div class="flex items-center">
              <span class="w-16 text-sm text-gray-600">综合:</span>
              <div class="flex-1 bg-gray-200 rounded-full h-2 mr-2">
                <div class="bg-purple-500 h-2 rounded-full" :style="{ width: horoscopeData.index.all }"></div>
              </div>
              <span class="text-sm font-semibold text-purple-600">{{ horoscopeData.index.all }}</span>
            </div>
            <div class="flex items-center">
              <span class="w-16 text-sm text-gray-600">爱情:</span>
              <div class="flex-1 bg-gray-200 rounded-full h-2 mr-2">
                <div class="bg-pink-500 h-2 rounded-full" :style="{ width: horoscopeData.index.love }"></div>
              </div>
              <span class="text-sm font-semibold text-pink-600">{{ horoscopeData.index.love }}</span>
            </div>
            <div class="flex items-center">
              <span class="w-16 text-sm text-gray-600">工作:</span>
              <div class="flex-1 bg-gray-200 rounded-full h-2 mr-2">
                <div class="bg-blue-500 h-2 rounded-full" :style="{ width: horoscopeData.index.work }"></div>
              </div>
              <span class="text-sm font-semibold text-blue-600">{{ horoscopeData.index.work }}</span>
            </div>
          </div>
          <div class="space-y-3">
            <div class="flex items-center">
              <span class="w-16 text-sm text-gray-600">财运:</span>
              <div class="flex-1 bg-gray-200 rounded-full h-2 mr-2">
                <div class="bg-green-500 h-2 rounded-full" :style="{ width: horoscopeData.index.money }"></div>
              </div>
              <span class="text-sm font-semibold text-green-600">{{ horoscopeData.index.money }}</span>
            </div>
            <div class="flex items-center">
              <span class="w-16 text-sm text-gray-600">健康:</span>
              <div class="flex-1 bg-gray-200 rounded-full h-2 mr-2">
                <div class="bg-red-500 h-2 rounded-full" :style="{ width: horoscopeData.index.health }"></div>
              </div>
              <span class="text-sm font-semibold text-red-600">{{ horoscopeData.index.health }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 宜忌事项 -->
      <div class="mb-8">
        <h3 class="text-xl font-bold text-gray-800 mb-4">今日宜忌</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="bg-green-50 p-4 rounded-lg">
            <div class="text-green-800 font-semibold mb-2">✅ 宜</div>
            <div class="text-green-700">{{ horoscopeData.todo.yi }}</div>
          </div>
          <div class="bg-red-50 p-4 rounded-lg">
            <div class="text-red-800 font-semibold mb-2">❌ 忌</div>
            <div class="text-red-700">{{ horoscopeData.todo.ji }}</div>
          </div>
        </div>
      </div>

      <!-- 详细运势 -->
      <div class="space-y-6">
        <h3 class="text-xl font-bold text-gray-800">详细运势</h3>
        
        <div class="bg-purple-50 p-4 rounded-lg">
          <h4 class="font-semibold text-purple-800 mb-2">🌟 综合运势</h4>
          <p class="text-gray-700 leading-relaxed">{{ horoscopeData.fortunetext.all }}</p>
        </div>

        <div class="bg-pink-50 p-4 rounded-lg">
          <h4 class="font-semibold text-pink-800 mb-2">💕 爱情运势</h4>
          <p class="text-gray-700 leading-relaxed">{{ horoscopeData.fortunetext.love }}</p>
        </div>

        <div class="bg-blue-50 p-4 rounded-lg">
          <h4 class="font-semibold text-blue-800 mb-2">💼 工作运势</h4>
          <p class="text-gray-700 leading-relaxed">{{ horoscopeData.fortunetext.work }}</p>
        </div>

        <div class="bg-green-50 p-4 rounded-lg">
          <h4 class="font-semibold text-green-800 mb-2">💰 财运</h4>
          <p class="text-gray-700 leading-relaxed">{{ horoscopeData.fortunetext.money }}</p>
        </div>

        <div class="bg-red-50 p-4 rounded-lg">
          <h4 class="font-semibold text-red-800 mb-2">🏥 健康运势</h4>
          <p class="text-gray-700 leading-relaxed">{{ horoscopeData.fortunetext.health }}</p>
        </div>
      </div>
    </div>

    <!-- 错误提示 -->
    <div v-if="error" class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
      <div class="flex items-center">
        <div class="text-red-600 mr-2">⚠️</div>
        <div class="text-red-800">{{ error }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// 星座数据
const constellations = [
  { value: 'aries', name: '白羊座', emoji: '♈' },
  { value: 'taurus', name: '金牛座', emoji: '♉' },
  { value: 'gemini', name: '双子座', emoji: '♊' },
  { value: 'cancer', name: '巨蟹座', emoji: '♋' },
  { value: 'leo', name: '狮子座', emoji: '♌' },
  { value: 'virgo', name: '处女座', emoji: '♍' },
  { value: 'libra', name: '天秤座', emoji: '♎' },
  { value: 'scorpio', name: '天蝎座', emoji: '♏' },
  { value: 'sagittarius', name: '射手座', emoji: '♐' },
  { value: 'capricorn', name: '摩羯座', emoji: '♑' },
  { value: 'aquarius', name: '水瓶座', emoji: '♒' },
  { value: 'pisces', name: '双鱼座', emoji: '♓' }
]

// 时间选项
const timeOptions = [
  { value: 'today', name: '今日运势' },
  { value: 'nextday', name: '明日运势' },
  { value: 'week', name: '本周运势' },
  { value: 'month', name: '本月运势' }
]

// 响应式数据
const selectedConstellation = ref('')
const selectedTime = ref('')
const loading = ref(false)
const error = ref('')
const horoscopeData = ref(null)

// 获取时间文本
const getTimeText = (time: string) => {
  const timeMap = {
    'today': '今日运势',
    'nextday': '明日运势',
    'week': '本周运势',
    'month': '本月运势'
  }
  return timeMap[time] || ''
}

// 查询星座运势
const queryHoroscope = async () => {
  if (!selectedConstellation.value || !selectedTime.value) {
    error.value = '请选择星座和时间'
    return
  }

  loading.value = true
  error.value = ''
  horoscopeData.value = null

  try {
    const response = await fetch(
      `https://api.vvhan.com/api/horoscope?type=${selectedConstellation.value}&time=${selectedTime.value}`
    )
    
    if (!response.ok) {
      throw new Error('网络请求失败')
    }
    
    const data = await response.json()
    
    if (data.success) {
      horoscopeData.value = data.data
    } else {
      error.value = data.message || '查询失败'
    }
  } catch (err) {
    error.value = '查询失败，请稍后重试'
    console.error('查询星座运势失败:', err)
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
/* 自定义样式 */
.bg-clip-text {
  -webkit-background-clip: text;
  background-clip: text;
}
</style> 