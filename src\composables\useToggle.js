/**
 * 切换状态 Composable
 * 提供布尔值切换功能
 */
import { ref } from 'vue'

export function useToggle(initialValue = false) {
  const value = ref(initialValue)
  
  // 切换方法
  const toggle = () => {
    value.value = !value.value
  }
  
  // 设置为true
  const setTrue = () => {
    value.value = true
  }
  
  // 设置为false
  const setFalse = () => {
    value.value = false
  }
  
  // 设置特定值
  const setValue = (newValue) => {
    value.value = Boolean(newValue)
  }
  
  return {
    value,
    toggle,
    setTrue,
    setFalse,
    setValue
  }
}
