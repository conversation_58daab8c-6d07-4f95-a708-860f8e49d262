# Vue3 本地组件写法完整教程

## 🎯 什么是本地组件？

本地组件是指在同一个文件中直接定义的组件，不需要单独的 `.vue` 文件。Vue3 完全支持这种写法，给开发者更多灵活性。

## 📝 基础写法

### 1. 最简单的本地组件
```javascript
const SimpleComponent = {
  template: `
    <div>
      <h3>Hello World!</h3>
      <p>这是一个最简单的本地组件</p>
    </div>
  `
}
```

### 2. 带数据的本地组件（Options API）
```javascript
const DataComponent = {
  template: `
    <div>
      <h3>{{ title }}</h3>
      <p>计数器: {{ count }}</p>
      <button @click="increment">增加</button>
    </div>
  `,
  data() {
    return {
      title: '带数据的组件',
      count: 0
    }
  },
  methods: {
    increment() {
      this.count++
    }
  }
}
```

### 3. 使用 Composition API 的本地组件
```javascript
const CompositionComponent = {
  template: `
    <div>
      <h3>{{ title }}</h3>
      <p>计数器: {{ count }}</p>
      <button @click="increment">增加</button>
      <p>双倍: {{ doubleCount }}</p>
    </div>
  `,
  setup() {
    const title = ref('Composition API 组件')
    const count = ref(0)
    
    const doubleCount = computed(() => count.value * 2)
    
    const increment = () => {
      count.value++
    }
    
    return {
      title,
      count,
      doubleCount,
      increment
    }
  }
}
```

## 🚀 高级用法

### 4. 带 Props 的本地组件
```javascript
const PropsComponent = {
  template: `
    <div class="card">
      <h3>{{ title }}</h3>
      <p>{{ content }}</p>
      <span class="badge">{{ type }}</span>
    </div>
  `,
  props: {
    title: {
      type: String,
      required: true
    },
    content: String,
    type: {
      type: String,
      default: 'info'
    }
  }
}

// 使用方式
// <PropsComponent title="标题" content="内容" type="success" />
```

### 5. 带事件的本地组件
```javascript
const EventComponent = {
  template: `
    <div>
      <input v-model="inputValue" @input="handleInput" />
      <button @click="handleClick">提交</button>
    </div>
  `,
  emits: ['input-change', 'submit'],
  setup(props, { emit }) {
    const inputValue = ref('')
    
    const handleInput = () => {
      emit('input-change', inputValue.value)
    }
    
    const handleClick = () => {
      emit('submit', inputValue.value)
    }
    
    return {
      inputValue,
      handleInput,
      handleClick
    }
  }
}
```

### 6. 带生命周期的本地组件
```javascript
const LifecycleComponent = {
  template: `
    <div>
      <h3>生命周期演示</h3>
      <p>当前时间: {{ currentTime }}</p>
    </div>
  `,
  setup() {
    const currentTime = ref(new Date().toLocaleTimeString())
    let timer = null
    
    onMounted(() => {
      console.log('组件已挂载')
      timer = setInterval(() => {
        currentTime.value = new Date().toLocaleTimeString()
      }, 1000)
    })
    
    onUnmounted(() => {
      console.log('组件即将卸载')
      if (timer) {
        clearInterval(timer)
      }
    })
    
    return {
      currentTime
    }
  }
}
```

## 🔧 实用技巧

### 7. 异步本地组件
```javascript
const AsyncLocalComponent = defineAsyncComponent(async () => {
  // 模拟异步加载
  await new Promise(resolve => setTimeout(resolve, 1000))
  
  return {
    template: `
      <div class="async-component">
        <h3>异步加载完成！</h3>
        <p>加载时间: {{ loadTime }}</p>
      </div>
    `,
    setup() {
      const loadTime = ref(new Date().toLocaleTimeString())
      return { loadTime }
    }
  }
})
```

### 8. 带插槽的本地组件
```javascript
const SlotComponent = {
  template: `
    <div class="card">
      <header class="card-header">
        <slot name="header">默认标题</slot>
      </header>
      <main class="card-body">
        <slot>默认内容</slot>
      </main>
      <footer class="card-footer">
        <slot name="footer">
          <button>默认按钮</button>
        </slot>
      </footer>
    </div>
  `
}

// 使用方式
/*
<SlotComponent>
  <template #header>
    <h2>自定义标题</h2>
  </template>
  
  <p>自定义内容</p>
  
  <template #footer>
    <button>自定义按钮</button>
  </template>
</SlotComponent>
*/
```

## 📋 在 `<script setup>` 中使用本地组件

```javascript
// 在 <script setup> 中定义本地组件
const LocalComponent = {
  template: `<div>本地组件</div>`
}

// 直接在模板中使用
// <LocalComponent />
```

## ⚡ 性能考虑

### 优点：
- ✅ 减少文件数量
- ✅ 组件定义就近原则
- ✅ 适合小型、简单的组件
- ✅ 快速原型开发

### 缺点：
- ❌ 不支持 `<style scoped>`
- ❌ 没有语法高亮（模板字符串）
- ❌ 不支持预处理器（Sass、TypeScript等）
- ❌ 难以进行单元测试

## 🎯 最佳实践

### 何时使用本地组件：
1. **简单的展示组件**
2. **临时的演示组件**
3. **配置对象形式的组件**
4. **动态生成的组件**

### 何时使用单文件组件：
1. **复杂的业务组件**
2. **需要样式的组件**
3. **可复用的组件**
4. **需要TypeScript的组件**

## 📖 完整示例

```vue
<template>
  <div>
    <h1>本地组件演示</h1>
    
    <!-- 使用本地组件 -->
    <SimpleCard title="卡片1" content="这是第一张卡片" />
    <SimpleCard title="卡片2" content="这是第二张卡片" />
    
    <CounterComponent @count-change="handleCountChange" />
  </div>
</template>

<script setup>
import { ref } from 'vue'

// 定义本地组件
const SimpleCard = {
  template: `
    <div class="card">
      <h3>{{ title }}</h3>
      <p>{{ content }}</p>
    </div>
  `,
  props: ['title', 'content']
}

const CounterComponent = {
  template: `
    <div>
      <button @click="increment">计数: {{ count }}</button>
    </div>
  `,
  emits: ['count-change'],
  setup(props, { emit }) {
    const count = ref(0)
    
    const increment = () => {
      count.value++
      emit('count-change', count.value)
    }
    
    return { count, increment }
  }
}

// 父组件逻辑
const handleCountChange = (newCount) => {
  console.log('计数变化:', newCount)
}
</script>

<style>
.card {
  border: 1px solid #ddd;
  padding: 1rem;
  margin: 0.5rem 0;
  border-radius: 4px;
}
</style>
```

## 🎉 总结

Vue3 的本地组件写法为开发者提供了极大的灵活性：

- **完全支持** `{ template: '...' }` 语法
- **兼容** Options API 和 Composition API
- **适合** 快速开发和原型制作
- **推荐** 与单文件组件结合使用

选择合适的组件定义方式，让您的Vue3开发更加高效！🚀
