<template>
  <div class="watch-container">
    <div class="user-card">
      <div class="card-header">
        <i class="fas fa-user-circle user-icon"></i>
        <h3 class="card-title">用户信息</h3>
      </div>
      <div class="card-body">
        <div class="info-item">
          <i class="fas fa-signature info-icon"></i>
          <span class="info-label">姓名：</span>
          <span class="info-value">{{ name }}</span>
        </div>
        <div class="info-item">
          <i class="fas fa-birthday-cake info-icon"></i>
          <span class="info-label">年龄：</span>
          <span class="info-value"><input type="number" v-model="age"></span>
        </div>
        <div class="info-item">
          <i class="fas fa-venus-mars info-icon"></i>
          <span class="info-label">性别：</span>
          <span class="info-value">{{ gender }}</span>
        </div>
        <div class="info-item">
          <i class="fas fa-briefcase info-icon"></i>
          <span class="info-label">职业：</span>
          <span class="info-value">{{ job }}</span>
        </div>
        <div>{{ name }}{{ age }}{{ gender }}{{ job }}</div>
      </div>
      <div class="card-footer">
        <button @click="checkName" class="action-btn">
          <i class="fas fa-edit"></i>
          修改名称
        </button>
        <button @click="switchToUsers" class="action-btn secondary-btn">
          <i class="fas fa-user-friends"></i>
          切换用户
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref,watch,reactive } from 'vue';
const name = ref('张三');
const age = ref(14);
const gender = ref('男');
const job = ref('学生');

const users = reactive({
    name: "王五",
    age: 17,
    gender: "女",
    job: "老师"

})

const checkName = () =>{
    name.value = "李四"
}

const switchToUsers = () => {
    name.value = users.name;
    age.value = users.age;
    gender.value = users.gender;
    job.value = users.job;
}

watch([name,()=>users.name], () => {
  console.log('name changed:', users.name);
});
</script>

<style scoped>
.watch-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.user-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  max-width: 400px;
  width: 100%;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.user-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
}

.card-header {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
  padding: 24px;
  text-align: center;
  position: relative;
}

.user-icon {
  font-size: 3rem;
  margin-bottom: 12px;
  display: block;
}

.card-title {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.card-body {
  padding: 32px 24px;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s ease;
}

.info-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.info-item:hover {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding-left: 8px;
  padding-right: 8px;
}

.info-icon {
  font-size: 1.2rem;
  color: #4facfe;
  margin-right: 12px;
  width: 20px;
  text-align: center;
}

.info-label {
  font-weight: 600;
  color: #333;
  margin-right: 8px;
  min-width: 60px;
}

.info-value {
  color: #666;
  font-weight: 500;
  flex: 1;
}

/* 卡片底部样式 */
.card-footer {
  padding: 20px 24px;
  background-color: #f8f9fa;
  border-top: 1px solid #e9ecef;
  text-align: center;
}

/* 按钮样式 */
.action-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 25px;
  font-size: 0.95rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
  display: inline-flex;
  align-items: center;
  gap: 8px;
  min-width: 140px;
  justify-content: center;
}

.action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}

.action-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 10px rgba(102, 126, 234, 0.3);
}

.action-btn i {
  font-size: 0.9rem;
}

/* 响应式按钮样式 */
@media (max-width: 768px) {
  .card-footer {
    padding: 16px 20px;
    flex-direction: column;
    align-items: center;
  }
  
  .action-btn {
    padding: 10px 20px;
    font-size: 0.9rem;
    min-width: 120px;
    width: 100%;
    max-width: 200px;
  }
}

/* 按钮布局 */
.card-footer {
  display: flex;
  gap: 12px;
  justify-content: center;
  flex-wrap: wrap;
}

/* 次要按钮样式 */
.secondary-btn {
  background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
  box-shadow: 0 4px 15px rgba(72, 187, 120, 0.3);
}

.secondary-btn:hover {
  background: linear-gradient(135deg, #38a169 0%, #2f855a 100%);
  box-shadow: 0 6px 20px rgba(72, 187, 120, 0.4);
}

/* 按钮动画 */
.action-btn {
  animation: fadeInUp 0.6s ease-out;
  animation-delay: 0.5s;
  animation-fill-mode: both;
}

.secondary-btn {
  animation-delay: 0.6s;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .watch-container {
    padding: 10px;
    min-height: 300px;
  }
  
  .user-card {
    max-width: 100%;
  }
  
  .card-header {
    padding: 20px;
  }
  
  .user-icon {
    font-size: 2.5rem;
  }
  
  .card-title {
    font-size: 1.3rem;
  }
  
  .card-body {
    padding: 24px 20px;
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.user-card {
  animation: fadeInUp 0.6s ease-out;
}

.info-item {
  animation: fadeInUp 0.6s ease-out;
}

.info-item:nth-child(1) { animation-delay: 0.1s; }
.info-item:nth-child(2) { animation-delay: 0.2s; }
.info-item:nth-child(3) { animation-delay: 0.3s; }
.info-item:nth-child(4) { animation-delay: 0.4s; }
</style>