<!-- Vue3 新特性演示组件 -->
<template>
  <div class="vue3-features-demo p-6 bg-white rounded-lg shadow-lg">
    <h2 class="text-2xl font-bold mb-6 text-gray-800">Vue3 新特性演示</h2>

    <!-- Fragment 演示 -->
    <div class="mb-8">
      <h3 class="text-lg font-semibold mb-4 text-purple-600">1. Fragment (多根节点)</h3>
      <div class="bg-gray-50 p-4 rounded">
        <p class="mb-2">Vue3 支持组件有多个根节点，不再需要包装div</p>
        <FragmentDemo />
      </div>
    </div>

    <!-- Teleport 演示 -->
    <div class="mb-8">
      <h3 class="text-lg font-semibold mb-4 text-purple-600">2. Teleport (传送门)</h3>
      <div class="bg-gray-50 p-4 rounded">
        <p class="mb-4">Teleport 可以将组件渲染到DOM的任何位置</p>
        <button 
          @click="showModal = true" 
          class="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600"
        >
          打开模态框
        </button>
        
        <!-- Teleport 将模态框传送到 body -->
        <Teleport to="body">
          <div v-if="showModal" class="modal-overlay" @click="showModal = false">
            <div class="modal-content" @click.stop>
              <h4 class="text-lg font-semibold mb-4">这是一个模态框</h4>
              <p class="mb-4">通过 Teleport 渲染到 body 元素中</p>
              <button 
                @click="showModal = false"
                class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
              >
                关闭
              </button>
            </div>
          </div>
        </Teleport>
      </div>
    </div>

    <!-- Suspense 演示 -->
    <div class="mb-8">
      <h3 class="text-lg font-semibold mb-4 text-purple-600">3. Suspense (异步组件)</h3>
      <div class="bg-gray-50 p-4 rounded">
        <p class="mb-4">Suspense 用于处理异步组件的加载状态</p>
        <button 
          @click="showAsyncComponent = !showAsyncComponent"
          class="px-4 py-2 bg-indigo-500 text-white rounded hover:bg-indigo-600 mb-4"
        >
          {{ showAsyncComponent ? '隐藏' : '显示' }}异步组件
        </button>
        
        <Suspense v-if="showAsyncComponent">
          <template #default>
            <AsyncDemo />
          </template>
          <template #fallback>
            <div class="flex items-center justify-center p-8">
              <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-500"></div>
              <span class="ml-2">加载中...</span>
            </div>
          </template>
        </Suspense>
      </div>
    </div>

    <!-- 自定义指令演示 -->
    <div class="mb-8">
      <h3 class="text-lg font-semibold mb-4 text-purple-600">4. 自定义指令 (v-focus)</h3>
      <div class="bg-gray-50 p-4 rounded">
        <p class="mb-4">Vue3 中自定义指令的生命周期钩子有所变化</p>
        <input 
          v-focus
          v-model="inputValue"
          placeholder="这个输入框会自动获得焦点"
          class="w-full p-2 border border-gray-300 rounded"
        />
        <p class="mt-2 text-sm text-gray-600">输入值: {{ inputValue }}</p>
      </div>
    </div>

    <!-- 全局属性演示 -->
    <div class="mb-8">
      <h3 class="text-lg font-semibold mb-4 text-purple-600">5. 全局属性访问</h3>
      <div class="bg-gray-50 p-4 rounded">
        <p class="mb-2">Vue3 中通过 getCurrentInstance 访问全局属性</p>
        <p class="text-sm text-gray-600">当前组件名称: {{ currentInstance?.type.name }}</p>
      </div>
    </div>

    <!-- Vue2 vs Vue3 对比说明 -->
    <div class="mt-8 p-4 bg-blue-50 border-l-4 border-blue-400">
      <h4 class="font-semibold text-blue-800 mb-2">🚀 Vue3 新特性优势:</h4>
      <ul class="text-sm text-blue-700 space-y-1">
        <li>• Fragment: 支持多根节点，减少不必要的包装元素</li>
        <li>• Teleport: 可以将组件渲染到任意DOM位置</li>
        <li>• Suspense: 优雅处理异步组件加载状态</li>
        <li>• 更好的Tree-shaking支持，按需引入</li>
        <li>• 更好的TypeScript集成</li>
      </ul>
    </div>
  </div>
</template>

<script>
import { ref, getCurrentInstance, onMounted } from 'vue'
import { defineAsyncComponent } from 'vue'

// Fragment 演示组件
const FragmentDemo = {
  template: `
    <p class="text-green-600">第一个根节点</p>
    <p class="text-blue-600">第二个根节点</p>
    <p class="text-red-600">第三个根节点</p>
  `
}

// 异步组件演示
const AsyncDemo = defineAsyncComponent(async () => {
  // 模拟异步加载
  await new Promise(resolve => setTimeout(resolve, 2000))
  
  return {
    template: `
      <div class="p-4 bg-green-100 rounded">
        <h4 class="font-semibold text-green-800">异步组件加载完成！</h4>
        <p class="text-green-700">这个组件经过2秒延迟加载</p>
      </div>
    `
  }
})

export default {
  name: 'Vue3FeaturesDemo',
  components: {
    FragmentDemo,
    AsyncDemo
  },
  // 自定义指令
  directives: {
    // Vue3 中指令的生命周期钩子名称有变化
    focus: {
      // Vue2 中是 inserted，Vue3 中是 mounted
      mounted(el) {
        el.focus()
      }
    }
  },
  setup() {
    // 响应式数据
    const showModal = ref(false)
    const showAsyncComponent = ref(false)
    const inputValue = ref('')
    
    // 获取当前组件实例
    const currentInstance = getCurrentInstance()

    return {
      showModal,
      showAsyncComponent,
      inputValue,
      currentInstance
    }
  }
}
</script>

<style scoped>
.vue3-features-demo {
  max-width: 800px;
  margin: 0 auto;
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  padding: 2rem;
  border-radius: 0.5rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  max-width: 400px;
  width: 90%;
}

/* 按钮悬停效果 */
button:hover {
  transform: translateY(-1px);
  transition: all 0.2s ease;
}
</style>
